#!/usr/bin/env python3
"""
Script de prueba para verificar el resaltado correcto de las expresiones matemáticas
"""

from manim import *

class TestHighlighting(Scene):
    """Escena de prueba para verificar los índices de resaltado"""
    
    def construct(self):
        # Configurar color de fondo
        self.camera.background_color = "#0F0F23"
        
        # Probar cada expresión
        self.test_example_1()
        self.wait(2)
        self.clear()
        
        self.test_example_2()
        self.wait(2)
        self.clear()
        
        self.test_example_3()
        self.wait(2)
        self.clear()
        
        self.test_example_4()
        self.wait(2)
    
    def test_example_1(self):
        """Probar: 10 - 2 + 5"""
        title = Text("Ejemplo 1: 10 - 2 + 5", font_size=36, color=WHITE)
        title.to_edge(UP)
        
        expr = MathTex("10 - 2 + 5", font_size=48, color=WHITE)
        expr.shift(UP * 1)
        
        # Mostrar la expresión completa con índices
        self.play(Write(title), Write(expr))
        
        # Resaltar "10 - 2" (debería ser índices 0:3)
        box1 = SurroundingRectangle(expr[0:3], color=RED, buff=0.1)
        label1 = Text("10 - 2 (índices 0:3)", font_size=24, color=RED)
        label1.next_to(expr, DOWN, buff=0.5)
        
        self.play(Create(box1), Write(label1))
        self.wait(1)
        
        # Mostrar toda la expresión
        self.play(FadeOut(box1), FadeOut(label1))
        box_all = SurroundingRectangle(expr, color=BLUE, buff=0.1)
        label_all = Text("Toda la expresión", font_size=24, color=BLUE)
        label_all.next_to(expr, DOWN, buff=0.5)
        
        self.play(Create(box_all), Write(label_all))
    
    def test_example_2(self):
        """Probar: 6 + 4 × 2 ÷ 4"""
        title = Text("Ejemplo 2: 6 + 4 × 2 ÷ 4", font_size=36, color=WHITE)
        title.to_edge(UP)
        
        expr = MathTex("6 + 4 \\times 2 \\div 4", font_size=48, color=WHITE)
        expr.shift(UP * 1)
        
        self.play(Write(title), Write(expr))
        
        # Resaltar "4 × 2" (probando índices 1:4)
        box1 = SurroundingRectangle(expr[1:4], color=PURPLE, buff=0.1)
        label1 = Text("4 × 2 (índices 1:4)", font_size=24, color=PURPLE)
        label1.next_to(expr, DOWN, buff=0.5)
        
        self.play(Create(box1), Write(label1))
        self.wait(1)
        
        # Cambiar a la expresión actualizada y resaltar "8 ÷ 4"
        self.play(FadeOut(box1), FadeOut(label1))
        
        expr2 = MathTex("6 + 8 \\div 4", font_size=48, color=WHITE)
        expr2.move_to(expr.get_center())
        
        self.play(Transform(expr, expr2))
        
        box2 = SurroundingRectangle(expr[1:4], color=YELLOW, buff=0.1)
        label2 = Text("8 ÷ 4 (índices 1:4)", font_size=24, color=YELLOW)
        label2.next_to(expr, DOWN, buff=0.5)
        
        self.play(Create(box2), Write(label2))
    
    def test_example_3(self):
        """Probar: (6 + 4) × 2 ÷ 4"""
        title = Text("Ejemplo 3: (6 + 4) × 2 ÷ 4", font_size=36, color=WHITE)
        title.to_edge(UP)
        
        expr = MathTex("(6 + 4) \\times 2 \\div 4", font_size=48, color=WHITE)
        expr.shift(UP * 1)
        
        self.play(Write(title), Write(expr))
        
        # Resaltar "(6 + 4)" (probando índice 0:1)
        box1 = SurroundingRectangle(expr[0:1], color=RED, buff=0.1)
        label1 = Text("(6 + 4) (índice 0:1)", font_size=24, color=RED)
        label1.next_to(expr, DOWN, buff=0.5)
        
        self.play(Create(box1), Write(label1))
        self.wait(1)
        
        # Cambiar a la expresión actualizada y resaltar "10 × 2"
        self.play(FadeOut(box1), FadeOut(label1))
        
        expr2 = MathTex("10 \\times 2 \\div 4", font_size=48, color=WHITE)
        expr2.move_to(expr.get_center())
        
        self.play(Transform(expr, expr2))
        
        box2 = SurroundingRectangle(expr[0:2], color=PURPLE, buff=0.1)
        label2 = Text("10 × 2 (índices 0:2)", font_size=24, color=PURPLE)
        label2.next_to(expr, DOWN, buff=0.5)
        
        self.play(Create(box2), Write(label2))
    
    def test_example_4(self):
        """Probar: 2³ + 10 ÷ 2 - 1"""
        title = Text("Ejemplo 4: 2³ + 10 ÷ 2 - 1", font_size=36, color=WHITE)
        title.to_edge(UP)
        
        expr = MathTex("2^3 + 10 \\div 2 - 1", font_size=48, color=WHITE)
        expr.shift(UP * 1)
        
        self.play(Write(title), Write(expr))
        
        # Resaltar "2³" (probando índice 0:1)
        box1 = SurroundingRectangle(expr[0:1], color=GREEN, buff=0.1)
        label1 = Text("2³ (índice 0:1)", font_size=24, color=GREEN)
        label1.next_to(expr, DOWN, buff=0.5)
        
        self.play(Create(box1), Write(label1))
        self.wait(1)
        
        # Cambiar a la expresión actualizada y resaltar "10 ÷ 2"
        self.play(FadeOut(box1), FadeOut(label1))
        
        expr2 = MathTex("8 + 10 \\div 2 - 1", font_size=48, color=WHITE)
        expr2.move_to(expr.get_center())
        
        self.play(Transform(expr, expr2))
        
        box2 = SurroundingRectangle(expr[1:3], color=YELLOW, buff=0.1)
        label2 = Text("10 ÷ 2 (índices 1:3)", font_size=24, color=YELLOW)
        label2.next_to(expr, DOWN, buff=0.5)
        
        self.play(Create(box2), Write(label2))
        self.wait(1)
        
        # Cambiar a la expresión actualizada y resaltar "8 + 5"
        self.play(FadeOut(box2), FadeOut(label2))
        
        expr3 = MathTex("8 + 5 - 1", font_size=48, color=WHITE)
        expr3.move_to(expr.get_center())
        
        self.play(Transform(expr, expr3))
        
        box3 = SurroundingRectangle(expr[0:2], color=BLUE, buff=0.1)
        label3 = Text("8 + 5 (índices 0:2)", font_size=24, color=BLUE)
        label3.next_to(expr, DOWN, buff=0.5)
        
        self.play(Create(box3), Write(label3))

if __name__ == "__main__":
    # Configuración de calidad para prueba rápida
    config.pixel_height = 720
    config.pixel_width = 1280
    config.frame_rate = 30
    
    # Ejecutar la escena de prueba
    scene = TestHighlighting()
    scene.render()
