#!/usr/bin/env python3
"""
Script simple para probar el resaltado
"""

from manim import *

class TestSimple(Scene):
    def construct(self):
        # Configurar color de fondo
        self.camera.background_color = "#0F0F23"
        
        # Probar Ejemplo 2
        print("=== EJEMPLO 2 ===")
        expr2 = MathTex("6", "+", "8", "\\div", "4", font_size=48, color=WHITE)
        print(f"Elementos: {len(expr2)}")
        for i, elem in enumerate(expr2):
            print(f"[{i}]: '{elem}'")
        
        expr2.shift(UP * 1)
        self.add(expr2)
        
        # Resaltar elementos [2], [3], [4] que deberían ser "8", "÷", "4"
        box2 = SurroundingRectangle(
            VGroup(expr2[2], expr2[3], expr2[4]),
            color=YELLOW,
            buff=0.1
        )
        self.add(box2)
        
        # Probar Ejemplo 4
        print("\n=== EJEMPLO 4 ===")
        expr4 = MathTex("8", "+", "5", "-", "1", font_size=48, color=WHITE)
        print(f"Elementos: {len(expr4)}")
        for i, elem in enumerate(expr4):
            print(f"[{i}]: '{elem}'")
        
        expr4.shift(DOWN * 1)
        self.add(expr4)
        
        # Resaltar elementos [0], [1], [2] que deberían ser "8", "+", "5"
        box4 = SurroundingRectangle(
            VGroup(expr4[0], expr4[1], expr4[2]),
            color=BLUE,
            buff=0.1
        )
        self.add(box4)
        
        self.wait(3)

if __name__ == "__main__":
    config.pixel_height = 720
    config.pixel_width = 1280
    config.frame_rate = 30
    
    scene = TestSimple()
    scene.render()
